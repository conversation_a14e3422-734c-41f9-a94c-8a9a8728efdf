import { ElementCoordinateMapping } from '../../agent/services/coordinate-resolution';
import { Action, ExtractionResult, FormField } from '../../agent/types/llm-result';
import {
  getHashedScriptUrl,
  initScreenCropper,
  initTensorFlowDetector,
  injectScript,
  injectTensorFlowJS,
  wrapForMainFrameOnly,
} from '../../browser';
import { withCdp } from '../../browser/client-api';
import { CDP } from '../../browser/simple-cdp';
import { ErrorService } from '../../common/error';
import { LLMService } from '../../llm/LLMService';
import { platformDetails } from '../../ui/constants';
import {
  BrowserDataAdapter,
  BrowserSession,
  RemoteBrowserService,
} from '../adapters/BrowserDataAdapter';
import { CDPBrowserDataAdapter } from '../adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from '../BrowserStateService';
import { CoordinatorDOBrowserStateRepository } from '../CoordinatorDOBrowserStateRepository';
import { ConnectionsWorkflowParams } from '../types';
import { StateChangeType } from '../types/StateChangeType';
import {
  getPlatformVersion,
  K_CUSTOM_VIEWPORT,
  pageStateResultComparisonPromptInstructions,
  PlatformDetectionConfig,
} from '../utils/constants';
import { sleep, withTimeout } from '../utils/helpers';
import { BrowserServiceFactory } from './BrowserServiceFactory';

export class BrowserManager {
  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private readonly env: Env;
  private readonly eventPayload: ConnectionsWorkflowParams;
  private readonly llmService: LLMService;

  private browserService: RemoteBrowserService;
  private browserStateService: BrowserStateService;
  private browserDataAdapter?: BrowserDataAdapter;

  private stateChangeAbortController = new AbortController();

  constructor({
    env,
    event,
    llmService,
  }: {
    env: Env;
    event: ConnectionsWorkflowParams;
    llmService: LLMService;
  }) {
    this.eventPayload = event;
    this.env = env;
    this.llmService = llmService;
    this.browserService = BrowserServiceFactory.createFromEnvironment(env);
    this.browserStateService = new BrowserStateService(
      new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
    );
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][browser-manager]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][browser-manager]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][browser-manager]', ...args);
  }

  async createCDPSession(): Promise<{ cdp: CDP; browserSession: BrowserSession }> {
    try {
      const browserSession = await this.browserService.getSession(this.eventPayload.sessionId);

      const cdp = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });

      await cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      return { cdp, browserSession };
    } catch (error) {
      await this.handleBrowserError(error);
      throw error;
    }
  }

  async setupBrowserSession(cdp: CDP) {
    const controlTabTarget = await cdp.Target.createTarget({ url: 'about:blank' });
    const mainTargetResponse = await cdp.Target.createTarget({ url: 'about:blank' });

    return {
      targetId: mainTargetResponse.targetId,
      controlTabTargetId: controlTabTarget.targetId,
    };
  }

  async setupTwoTabArchitecture({
    cdp,
    targetId,
    controlTabTargetId,
    browserSessionWSEndpoint,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
    browserSessionWSEndpoint: string;
  }) {
    try {
      const sessionIds = await this.attachControlAndTargetTabs({
        cdp: cdp,
        targetId: targetId,
        controlTabTargetId: controlTabTargetId,
      });

      await this.injectControlTabScriptsEarly({
        cdp: cdp,
        targetId: targetId,
        controlTabSessionId: sessionIds.controlTabSessionId,
        browserSessionWSEndpoint: browserSessionWSEndpoint,
      });

      await this.injectBrowserControllerProxyInTarget({
        cdp: cdp,
        targetSessionId: sessionIds.targetSessionId,
      });

      //Load browser state, if available
      if (!this.browserDataAdapter) {
        this.browserDataAdapter = new CDPBrowserDataAdapter(cdp, sessionIds.targetSessionId);
      }

      await this.browserStateService.loadBrowserStateToPage(
        this.browserDataAdapter,
        this.eventPayload.userId,
        this.eventPayload.platformId,
      );

      return sessionIds;
    } catch (error) {
      this.log(`An error occurred ${JSON.stringify(error)}`);
      await this.handleBrowserError(error);
      throw error;
    }
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  async ensureViewportSettings({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    await cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      targetSessionId,
    );
  }

  async navigateToLoginPage({
    cdp,
    targetSessionId,
    controlTabSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    controlTabSessionId: string;
  }): Promise<void> {
    // goto github.com on controlTapSessionId
    const pageLoadControlTab = this.waitForPageLoad(cdp);
    await cdp.Page.navigate(
      {
        url: platformDetails[this.eventPayload.platformId].loginLink,
      },
      controlTabSessionId,
    );

    await pageLoadControlTab;

    const pageLoad = this.waitForPageLoad(cdp);

    await cdp.Page.navigate(
      {
        url: platformDetails[this.eventPayload.platformId].loginLink,
      },
      targetSessionId,
    );

    await pageLoad;
  }

  async setupExecutionContextListener({
    cdp,
    targetId,
  }: {
    cdp: CDP;
    targetId: string;
  }): Promise<number> {
    // remove promise. I just want to update the executioncontextId
    return await new Promise((resolve) => {
      cdp.Runtime.addEventListener('executionContextCreated', (fullData: { params: any }) => {
        const { context } = fullData.params;
        if (context.name === 'kaku-target-world' && context.auxData.frameId === targetId) {
          cdp?.Runtime.removeEventListener('executionContextCreated', (e) => {});
          resolve(context.id);
        }
      });
    });
  }

  /**
   * Inject target tab scripts for captcha handling
   * Control tab scripts are already injected early, so only inject target tab scripts
   */
  async injectTargetTabScriptsForCaptcha({
    cdp,
    targetSessionId,
    executionContextId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    executionContextId: number;
  }) {
    // Inject target tab scripts only (control tab scripts already injected early)
    this.log('Injecting scripts for captcha handling with executionContextId:', executionContextId);

    await this.injectTargetTabScripts({ cdp, targetSessionId, executionContextId });
  }

  async executeFormActions(cdp: CDP, targetSessionId: string, actions: Action[]): Promise<void> {
    try {
      // Sort actions by order, ensuring submit actions are last
      const sortedActions = actions.sort((a, b) => a.order - b.order);

      // Process each action in order
      for (const action of sortedActions) {
        await this.executeAction({ cdp: cdp, targetSessionId: targetSessionId, action: action });
      }

      this.log('✓ Form actions executed successfully');
    } catch (error) {
      this.error(`✖ Error executing form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  async executeAIActions(
    cdp: CDP,
    targetSessionId: string,
    aiActionsFields: FormField[],
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    try {
      if (aiActionsFields.length === 0) {
        this.log('No AI actions to execute');
        return;
      }

      aiActionsFields.map(async (aiActionField) => {
        await this.handleKeepMeLoggedIn(
          cdp,
          targetSessionId,
          aiActionField,
          elementCoordinateMapping,
        );
        return;
      });
    } catch (error) {
      this.error(`Error executing AI form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  private async handleKeepMeLoggedIn(
    cdp: CDP,
    targetSessionId: string,
    action: FormField,
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    if (action.checked) {
      // early exit, no need to click as the checkbox is already checked
      return;
    }
    const coordinates = elementCoordinateMapping?.[action.id];

    if (!coordinates) {
      throw new Error('No coordinates found for keep-me-logged-in action');
    }

    await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });
  }

  private async executeAction({
    cdp,
    targetSessionId,
    action,
  }: {
    cdp: CDP;
    targetSessionId: string;
    action: Action;
  }): Promise<void> {
    const { type, coordinates, name, value } = action;

    if (type === 'fill' && value) {
      await this.fillTextField({
        cdp: cdp,
        targetSessionId: targetSessionId,
        coordinates: coordinates,
        name: name,
        value: value,
      });
    } else if (type === 'click' || type === 'select') {
      await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });
    }
  }

  private async fillTextField({
    cdp,
    targetSessionId,
    coordinates,
    name,
    value,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
    name: string;
    value: string;
  }): Promise<void> {
    try {
      // Click on the field
      await this.clickAt({ cdp: cdp, targetSessionId: targetSessionId, coordinates: coordinates });

      // Triple-click to select all text
      await this.tripleClickAt({
        cdp: cdp,
        targetSessionId: targetSessionId,
        coordinates: coordinates,
      });

      // Clear any existing text
      await this.pressBackspace({ cdp: cdp, targetSessionId: targetSessionId });

      // Type the value
      await this.typeText({ cdp: cdp, targetSessionId: targetSessionId, text: value });
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async clickAt({
    cdp,
    targetSessionId,
    coordinates,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
  }): Promise<void> {
    try {
      // Mouse down
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        targetSessionId,
      );

      // Mouse up to complete click
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        targetSessionId,
      );
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async tripleClickAt({
    cdp,
    targetSessionId,
    coordinates,
  }: {
    cdp: CDP;
    targetSessionId: string;
    coordinates: { x: number; y: number };
  }): Promise<void> {
    for (let i = 0; i < 3; i++) {
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        targetSessionId,
      );
      await cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        targetSessionId,
      );
    }
  }

  private async pressBackspace({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    await cdp.Input.dispatchKeyEvent(
      {
        type: 'keyDown',
        windowsVirtualKeyCode: 8,
        key: 'Backspace',
      },
      targetSessionId,
    );
    await cdp.Input.dispatchKeyEvent(
      {
        type: 'keyUp',
        key: 'Backspace',
      },
      targetSessionId,
    );
  }

  private async typeText({
    cdp,
    targetSessionId,
    text,
  }: {
    cdp: CDP;
    targetSessionId: string;
    text: string;
  }): Promise<void> {
    try {
      // Set the clipboard content
      await cdp.Input.insertText(
        {
          text: text,
        },
        targetSessionId,
      );

      this.log(
        `✓ Successfully pasted text: "${text.substring(0, 2)}${text.length > 4 ? '...' : ''}"`,
      );
    } catch (error) {
      throw Error(`✖ Error pasting text: ${(error as Error).message}`);
    }
  }

  cancelActiveBrowserStateChangeListeners() {
    this.stateChangeAbortController.abort();
  }

  async waitForPageLoad(cdp: CDP): Promise<boolean | null> {
    return await new Promise<boolean | null>((resolve) => {
      const handler = () => {
        this.log('Page Loaded event fired', { pageLoaded: true });
        resolve(true);
      };

      cdp?.Page.addEventListener('loadEventFired', handler);

      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        this.log('Page load State change listener cancelled by abort controller');
        cdp?.Page.removeEventListener('loadEventFired', handler);
        resolve(null);
      });
    });
  }

  async waitForPageUpdateAfterSubmission({
    cdp,
    currentFormVisionResult,
    targetSessionId,
    configSettings,
  }: {
    cdp: CDP;
    currentFormVisionResult: ExtractionResult;
    targetSessionId: string;
    configSettings: PlatformDetectionConfig;
  }): Promise<StateChangeType | null> {
    // Strategy 1: Traditional page load (for legacy platforms)
    const pageLoad = this.waitForPageLoad(cdp).then((result) => {
      if (result === true) {
        this.log('✓ Page load event fired - cancelling visual change detection');
        this.stateChangeAbortController.abort();
        return StateChangeType.PAGE_LOAD;
      }
    });

    // Strategy 2: Platform-aware visual change detection (for SPAs)
    const llmDetection = this.compareVisualChangesUsingLLM({
      cdp: cdp,
      targetSessionId: targetSessionId,
      config: configSettings,
      currentFormVisionResult: currentFormVisionResult,
    }).then((result) => {
      if (result === true) {
        this.stateChangeAbortController.abort();
        return StateChangeType.LLM_DETECTION;
      }
    });

    // Race all strategies with platform-specific timeout
    const result = await withTimeout(
      Promise.race([pageLoad, llmDetection /*, visualChanges*/]),
      configSettings.maxWaitTime,
    );

    return typeof result === 'string' ? (result as StateChangeType) : null;
  }

  private async compareVisualChangesUsingLLM({
    cdp,
    targetSessionId,
    config,
    currentFormVisionResult,
  }: {
    cdp: CDP;
    targetSessionId: string;
    config: PlatformDetectionConfig;
    currentFormVisionResult: ExtractionResult;
  }): Promise<boolean | null> {
    const layoutMetrics = await cdp.Page.getLayoutMetrics(undefined, targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(this.eventPayload.platformId, this.env);

    const startTime = Date.now();
    const { checkInterval, maxWaitTime, minChangeDetectionDelay = 0 } = config;

    // Wait for minimum delay before starting to check for LLM changes
    await sleep(minChangeDetectionDelay);

    return new Promise<boolean | null>(async (resolve) => {
      let resultFound = false;
      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        this.log('LLM State change listener cancelled by abort controller');
        resultFound = true;
        resolve(null);
      });

      let storedScreenshot: string | null = null;

      while (!resultFound) {
        if (Date.now() - startTime > maxWaitTime) {
          this.log('→ LLM change detection timeout reached', resultFound);
          resultFound = true;
          resolve(null);
        }

        const currentScreenshot = await this.captureScreenshotForComparison({
          cdp: cdp,
          targetSessionId: targetSessionId,
        });

        if (storedScreenshot !== currentScreenshot && currentScreenshot != null) {
          const comparisonResult = await this.llmService.detectStateChangeFromUserAgentState({
            linkId: this.eventPayload.linkId,
            platform: this.eventPayload.platformId,
            prompt: pageStateResultComparisonPromptInstructions,
            agentVisionResultState: currentFormVisionResult,
            screenshot: currentScreenshot,
            skipCache: this.env.SKIP_CACHE,
            viewportWidth: viewPort.width,
            viewportHeight: viewPort.height,
            version: version,
          });
          storedScreenshot = currentScreenshot;

          if (comparisonResult) {
            resultFound = true;
            resolve(true);
          }
        }

        await sleep(checkInterval);
      }

      resolve(null);
    });
  }

  async captureScreenshot({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    executionContextId: number;
  }): Promise<string> {
    const screenshot = await cdp.Page.captureScreenshot(
      {
        format: 'webp',
        captureBeyondViewport: false,
        quality: 80,
      },
      targetSessionId,
    );

    return screenshot.data;
  }

  /**
   * Captures a screenshot optimized for comparison
   * Returns base64 PNG string for direct pixelmatch processing
   */
  private async captureScreenshotForComparison({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<string | null> {
    const screenshot = await cdp.Page.captureScreenshot(
      {
        format: 'png',
        captureBeyondViewport: false,
      },
      targetSessionId,
    );

    return screenshot.data;
  }

  /**
   * Inject scripts for the target tab (user interaction)
   */
  private async injectTargetTabScripts({
    cdp,
    targetSessionId,
    executionContextId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    executionContextId: number;
  }) {
    // Get script URLs
    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const screenCropperUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screen-cropper.min.js',
    );

    const captchaDetectorUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector.min.js',
    );

    const screenshotComparisonUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screenshot-comparison.min.js',
    );

    const captchaDetectorTfUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector-tf.min.js',
    );

    const tfModelBundleUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'tf-model-bundle.min.js',
    );

    try {
      const layoutMetrics = await cdp.Page.getLayoutMetrics(undefined, targetSessionId);
      const viewport = {
        width: layoutMetrics.cssLayoutViewport.clientWidth,
        height: layoutMetrics.cssLayoutViewport.clientHeight,
      };

      // Inject core scripts in parallel
      await Promise.all([
        injectScript(cdp, crossTabCommUrl, executionContextId, targetSessionId),
        injectScript(cdp, screenshotComparisonUrl, executionContextId, targetSessionId),
        injectScript(cdp, screenCropperUrl, executionContextId, targetSessionId),
        injectScript(cdp, captchaDetectorUrl, executionContextId, targetSessionId),
        injectTensorFlowJS(cdp, executionContextId, targetSessionId),
      ]);

      // Initialize proxy browser controller first (screen cropper depends on it)
      const clientApis = withCdp(cdp, executionContextId, targetSessionId);
      await clientApis.BrowserController.init();
      // Inject TensorFlow-dependent scripts and initialize screen cropper
      await Promise.all([
        injectScript(cdp, captchaDetectorTfUrl, executionContextId, targetSessionId),
        injectScript(cdp, tfModelBundleUrl, executionContextId, targetSessionId),
        initScreenCropper(
          cdp,
          `${this.env.KAKU_WS_ENDPOINT}/v1/agents/connections/${this.eventPayload.linkId}`,
          executionContextId,
          viewport,
          targetSessionId,
        ),
      ]);

      // Initialize TensorFlow detector
      await initTensorFlowDetector(cdp, executionContextId, viewport, targetSessionId);
    } catch (error) {
      this.error('Error injecting target tab scripts:', error);
      throw error;
    }
  }

  private async attachControlAndTargetTabs({
    cdp,
    targetId,
    controlTabTargetId,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
  }) {
    //Handle attaching to control and target tabs
    const attachToControlTargetResponse = await cdp.Target.attachToTarget({
      targetId: controlTabTargetId,
      flatten: true,
    });

    const attachToMainTargetResponse = await cdp.Target.attachToTarget({
      targetId: targetId,
      flatten: true,
    });

    // Enable required domains for both tabs
    this.log('About to enable requirements');
    await this.enableDomainsForBothTabs({
      cdp: cdp,
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    });

    return {
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    };
  }

  /**
   * Inject control tab scripts early using addScriptToEvaluateOnNewDocument
   * This ensures scripts persist across page navigations
   */
  private async injectControlTabScriptsEarly({
    cdp,
    targetId,
    controlTabSessionId,
    browserSessionWSEndpoint,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabSessionId: string;
    browserSessionWSEndpoint: string;
  }): Promise<void> {
    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const persistentCDPControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'persistent-cdp-controller.min.js',
    );

    try {
      // Fetch script contents for injection
      const [crossTabCommScript, persistentCDPControllerScript] = await Promise.all([
        fetch(crossTabCommUrl).then((r) => r.text()),
        fetch(persistentCDPControllerUrl).then((r) => r.text()),
      ]);

      // the delay is added to wait for the persistentCDP Controller script to be available
      const initScript = `
        (async () => {
          while (!window.persistentCDPController) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
          await window.persistentCDPController.init(
            '${browserSessionWSEndpoint}',
            '${targetId}'
          );

        })();
      `;

      // Inject scripts using addScriptToEvaluateOnNewDocument for persistence
      await Promise.all([
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(crossTabCommScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(persistentCDPControllerScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(initScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
      ]);
    } catch (error) {
      this.error('Error injecting control tab scripts early:', error);
      throw error;
    }
  }

  private async injectBrowserControllerProxyInTarget({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }) {
    const browserControllerProxyUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller-proxy.min.js',
    );

    const browserControllerProxyScript = await fetch(browserControllerProxyUrl).then((r) =>
      r.text(),
    );

    // These scripts will persist across page navigations
    await cdp!.Page.addScriptToEvaluateOnNewDocument(
      {
        source: browserControllerProxyScript,
        worldName: 'kaku-target-world',
      },
      targetSessionId,
    );
  }

  /**
   * Enable required CDP domains for both control and target tabs
   */
  private async enableDomainsForBothTabs({
    cdp,
    controlTabSessionId,
    targetSessionId,
  }: {
    cdp: CDP;
    controlTabSessionId: string;
    targetSessionId: string;
  }): Promise<void> {
    // Enable domains for control tab
    await Promise.all([
      cdp.Page.enable(undefined, controlTabSessionId),
      cdp.Runtime.enable(undefined, controlTabSessionId),
    ]);

    // Enable domains for target tab
    await Promise.all([
      cdp.Page.enable(undefined, targetSessionId),
      cdp.Runtime.enable(undefined, targetSessionId),
    ]);

    // Disable content security policy for both tabs
    await Promise.all([
      cdp.Page.setBypassCSP({ enabled: true }, controlTabSessionId),
      cdp.Page.setBypassCSP({ enabled: true }, targetSessionId),
    ]);

    await Promise.all([cdp.WebAuthn.enable(undefined, targetSessionId)]);

    const virtualAuthenticatorOptions = {
      protocol: 'ctap2' as const,
      transport: 'internal' as const,
      hasResidentKey: true,
      hasUserVerification: true,
      isUserVerified: true,
      automaticPresenceSimulation: true,
    };
    await cdp.WebAuthn.addVirtualAuthenticator(
      { options: virtualAuthenticatorOptions },
      targetSessionId,
    );
  }

  private async handleBrowserError(error: any): Promise<void> {
    this.error('An error occurred while setting up the browser session:', error);
    if (error instanceof Error) {
      await ErrorService.handleBrowserConnectionError(
        error,
        {
          ...this.eventPayload,
          referenceId: this.eventPayload.linkId,
        },
        this.env,
      );
    }
  }

  /**
   * Handle social login button clicks by setting up race condition listeners
   * for new tab/window creation and same-page redirects
   */
  async handleSocialLoginClick(
    cdp: CDP,
    targetSessionId: string,
  ): Promise<{ newTargetId?: string; newSessionId?: string; redirected: boolean }> {
    // Set up race condition listeners
    const racePromise = this.setupSocialLoginRaceCondition(cdp, targetSessionId);
    const result = await racePromise;
    console.log('result', result);
    return result;
  }

  /**
   * Set up race condition for social login events:
   * 1. Target.targetCreated for new tab
   * 2. Target.targetCreated for new window/popup
   * 3. Page.loadEventFired for same-page redirect
   */
  private async setupSocialLoginRaceCondition(
    cdp: CDP,
    _currentTargetSessionId: string,
  ): Promise<{ newTargetId?: string; newSessionId?: string; redirected: boolean }> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        cleanup();
        resolve({ redirected: false });
      }, 10000); // 10 second timeout

      let resolved = false;
      const cleanup = () => {
        if (resolved) return;
        resolved = true;
        clearTimeout(timeout);
        cdp.Target.removeEventListener('targetAttached', onTargetCreated);
        cdp.Page.removeEventListener('loadEventFired', onPageLoad);
      };

      // Event 1 & 2: Handle new target creation (tabs/windows)
      const onTargetCreated = async (event: any) => {
        try {
          const { targetInfo } = event.params;
          this.log(`🎯 New target created: ${targetInfo.type} - ${targetInfo.url}`);

          if (targetInfo.type === 'page') {
            // Attach to the new target
            const attachResponse = await cdp.Target.attachToTarget({
              targetId: targetInfo.targetId,
              flatten: true,
            });

            // Enable required domains
            await cdp.Page.enable(undefined, attachResponse.sessionId);
            await cdp.Runtime.enable(undefined, attachResponse.sessionId);

            cleanup();
            resolve({
              newTargetId: targetInfo.targetId,
              newSessionId: attachResponse.sessionId,
              redirected: true,
            });
          }
        } catch (error) {
          this.error('Error handling target creation:', error);
          cleanup();
          reject(error);
        }
      };

      // Event 3: Handle same-page redirect
      const onPageLoad = async () => {
        try {
          this.log(`📄 Page load event fired on current target`);
          cleanup();
          resolve({ redirected: true });
        } catch (error) {
          this.error('Error handling page load:', error);
          cleanup();
          reject(error);
        }
      };

      // Set up event listeners
      cdp.Target.addEventListener('targetAttached', onTargetCreated);
      cdp.Page.addEventListener('loadEventFired', onPageLoad);
    });
  }

  /**
   * Set up script injection and monitoring for a new social login target
   */
  async setupNewSocialLoginTarget(cdp: CDP, targetId: string, sessionId: string): Promise<void> {
    try {
      this.log(`🔧 Setting up new social login target: ${targetId}`);

      // Set viewport and device metrics
      await this.ensureViewportSettings({ cdp, targetSessionId: sessionId });

      // Get execution context
      const executionContextId = await this.setupExecutionContextListener({
        cdp,
        targetId,
      });

      // Inject essential scripts
      await this.injectBrowserControllerProxyInTarget({
        cdp,
        targetSessionId: sessionId,
      });

      // Set up input focus listener
      const { setupInputFocusListener } = await import('../../browser');
      await setupInputFocusListener(cdp, sessionId);

      this.log(`✅ Social login target setup complete: ${targetId}`);
    } catch (error) {
      this.error('Error setting up social login target:', error);
      throw error;
    }
  }
}
