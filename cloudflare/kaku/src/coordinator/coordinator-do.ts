import { Agent, AgentContext } from 'agents';
import { platformDetails, PlatformTypes } from '../ui/constants';
import {
  CoordinatorState,
  PlatformMetadata,
  LinkInfo,
  CoordinatorUtils,
  COORDINATOR_CONSTANTS,
  CreateLinkResponse,
  ConnectionsResponse,
  Connection,
  UserSessionData,
} from '../shared/coordinator-types';

/**
 * CoordinatorDO - Manages links and Agent DOs for platform connections
 *
 * This Agent serves as a central coordinator for managing
 * links, Agent DO lifecycle, and platform-specific retry logic.
 */
export class CoordinatorDO extends Agent<Env, CoordinatorState> {
  initialState: CoordinatorState = {
    platforms: [],
  };

  constructor(ctx: AgentContext, env: Env) {
    super(ctx, env);
  }

  /**
   * Get or create platform metadata
   */
  private getOrCreatePlatform(platformId: PlatformTypes): PlatformMetadata {
    let platform = this.state.platforms.find((p) => p.id === platformId);

    if (!platform) {
      platform = {
        id: platformId,
        links: [],
        connected: false,
        retryCount: 0,
        sessionData: undefined,
      };

      this.setState({
        platforms: [...this.state.platforms, platform],
      });
    }

    return platform;
  }

  // RPC Methods for link management

  /**
   * Create a new link for platform connection
   */
  async createLink(platform: PlatformTypes, userId: string): Promise<CreateLinkResponse> {
    const platformData = this.getOrCreatePlatform(platform);

    if (platformData.connected) {
      throw new Error(`User is already connected to ${platform}`);
    }

    // Check if retries are exhausted for this platform
    if (!CoordinatorUtils.canRetry(platformData.retryCount)) {
      throw new Error(`Retry limit exceeded for ${platform}`);
    }

    const { newLinkId, newAgentId } = await this.setupNewLink(platformData, userId);

    const url = `${this.env.KAKU_API_ENDPOINT}/${platform}/${newLinkId}?userId=${userId}`;
    const now = Date.now();
    const expiresAt = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;

    const linkInfo: LinkInfo = {
      linkId: newLinkId,
      status: 'active',
      url,
      createdAt: now,
      expiresAt,
      agentId: newAgentId,
    };

    const updatedPlatforms = this.state.platforms.map((p) =>
      p.id === platform ? { ...p, links: [...p.links, linkInfo] } : p,
    );

    this.setState({
      platforms: updatedPlatforms,
    });

    await this.schedule(new Date(expiresAt), 'handleLinkExpiration', newLinkId);

    return { linkId: newLinkId, url, expiresAt };
  }

  /**
   * Get status of a specific link
   */
  getStatus(linkId: string): LinkInfo | null {
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.linkId === linkId);
      if (link) {
        if (Date.now() > link.expiresAt && link.status === 'active') {
          this.expireLink(linkId);
          return { ...link, status: 'expired' };
        }
        return link;
      }
    }
    return null;
  }

  /**
   * Revoke a specific link
   */
  async revokeLink(linkId: string): Promise<boolean> {
    let targetPlatform: PlatformMetadata | null = null;
    let targetLink: LinkInfo | null = null;

    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.linkId === linkId);
      if (link) {
        targetPlatform = platform;
        targetLink = link;
        break;
      }
    }

    if (!targetPlatform || !targetLink || targetLink.status !== 'active') {
      return false;
    }

    // Expire the link
    this.expireLink(linkId);

    // Schedule deletion after 7 days
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);

    console.log(`[CoordinatorDO] Revoked link ${linkId} for platform ${targetPlatform.id}`);
    return true;
  }

  /**
   * Get all links for a specific platform
   */
  async getPlatformLinks(platformId: PlatformTypes): Promise<{ links: LinkInfo[] }> {
    const platform = this.state.platforms.find((p) => p.id === platformId);

    if (!platform) {
      return { links: [] };
    }

    // Update expired links before returning
    const updatedLinks = platform.links.map((link) => {
      if (Date.now() > link.expiresAt && link.status === 'active') {
        this.expireLink(link.linkId);
        return { ...link, status: 'expired' as const };
      }
      return link;
    });

    return { links: updatedLinks };
  }

  /**
   * Generic reset method that properly closes browser sessions before creating new Agent DOs
   */
  async resetLink(
    linkId: string,
    userId: string,
    platformId: PlatformTypes,
  ): Promise<{ newLinkId: string; newUrl: string; expiresAt: number } | null> {
    // First, identify and cleanup the active browser session for the current link
    const agentId = this.getAgentIdForLink(linkId);
    if (agentId) {
      const agentStub = this.env.Connections.idFromName(agentId);
      const agent = this.env.Connections.get(agentStub);

      // Cleanup browser session before revoking the link
      await agent.cleanupResources();
      console.log(
        `[CoordinatorDO] Successfully cleaned up browser session for Agent DO: ${agentId}`,
      );
    } else {
      console.log(`[CoordinatorDO] No active Agent DO found for link: ${linkId}`);
    }

    // Then, revoke the existing link
    const revoked = await this.revokeLink(linkId);
    if (!revoked) {
      console.log(`[CoordinatorDO] Failed to revoke link: ${linkId}`);
      return null;
    }

    // Increment retry count for this platform
    // const updatedPlatforms = this.state.platforms.map((platform) =>
    //   platform.id === platformId ? { ...platform, retryCount: platform.retryCount + 1 } : platform,
    // );
    // this.setState({ platforms: updatedPlatforms });

    // Then, create a new link
    const newLinkResponse = await this.createLink(platformId, userId);

    return {
      newLinkId: newLinkResponse.linkId,
      newUrl: newLinkResponse.url,
      expiresAt: newLinkResponse.expiresAt,
    };
  }

  /**
   * Handle link expiration (called by scheduler)
   */
  async handleLinkExpiration(linkId: string): Promise<void> {
    console.log(`[CoordinatorDO] Handling expiration for link: ${linkId}`);
    this.expireLink(linkId);

    // Schedule deletion after 7 days
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Handle link deletion (called by scheduler)
   */
  async handleLinkDeletion(linkId: string): Promise<void> {
    const agentId = this.getAgentIdForLink(linkId);
    if (!agentId) {
      throw new Error(`Agent ID not found for link: ${linkId}`);
    }
    await this.deleteAgentDO(agentId);
  }

  // Private helper methods

  /**
   * Create a new Agent DO for the link
   */
  private async createAgentDO(
    linkId: string,
    platform: PlatformTypes,
    userId: string,
  ): Promise<string> {
    const agentId = linkId;

    const agentStub = this.env.Connections.idFromName(agentId);
    const agent = this.env.Connections.get(agentStub);
    agent.setup(platform, userId, linkId);
    console.log(`[CoordinatorDO] Created Agent DO: ${agentId} for platform: ${platform}`);
    await this.schedule(
      new Date(Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY),
      'deleteAgentDO',
      agentId,
    );
    return agentId;
  }

  /**
   * Delete an Agent DO
   */
  async deleteAgentDO(agentId: string): Promise<void> {
    try {
      const agentStub = this.env.Connections.idFromName(agentId);
      const agent = this.env.Connections.get(agentStub);

      // Delete all data from the Agent DO
      await agent.deleteAll();
    } catch (error) {
      console.error(`[CoordinatorDO] Failed to delete Agent DO ${agentId}:`, error);
    }
  }

  async deleteAll() {
    // deleting all the agent DO straight away
    await this.deleteAllConnections();
    // deleting all the data from the coordinator DO
    await this.ctx.storage.deleteAll();
    await this.ctx.storage.deleteAlarm();
  }

  /**
   * Expire a link and schedule deletion
   */
  private expireLink(linkId: string): void {
    // Find and update the link status across all platforms
    const updatedPlatforms = this.state.platforms.map((platform) => ({
      ...platform,
      links: platform.links.map((link) =>
        link.linkId === linkId ? { ...link, status: 'expired' as const } : link,
      ),
    }));

    this.setState({
      platforms: updatedPlatforms,
    });
  }

  private async setupNewLink(targetPlatform: PlatformMetadata, userId: string) {
    const newLinkId = CoordinatorUtils.generateLinkId();
    const url = `${this.env.KAKU_API_ENDPOINT}/${targetPlatform.id}/${newLinkId}?userId=${userId}`;
    const now = Date.now();
    const expiresAt = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;

    const newAgentId = await this.createAgentDO(newLinkId, targetPlatform.id, userId);

    const newLinkInfo: LinkInfo = {
      linkId: newLinkId,
      status: 'active',
      url,
      createdAt: now,
      expiresAt,
      agentId: newAgentId,
    };
    return { newLinkInfo, newLinkId, newAgentId };
  }

  /**
   * Mark a platform as connected for this user
   */
  async markPlatformConnected(linkId: string): Promise<void> {
    const updatedPlatforms = this.state.platforms.map((platform) => {
      const linkIndex = platform.links.findIndex((l) => l.linkId === linkId);
      if (linkIndex !== -1) {
        const updatedLinks = [...platform.links];
        updatedLinks[linkIndex] = {
          ...updatedLinks[linkIndex],
          status: 'connected',
          connectedAt: Date.now(),
        };

        return {
          ...platform,
          connected: true,
          links: updatedLinks,
        };
      }
      return platform;
    });

    this.setState({
      platforms: updatedPlatforms,
    });

    // Schedule deletion right away for connected links (7 days)
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Disconnect a platform for this user
   */
  async disconnectPlatform(platformId: PlatformTypes): Promise<boolean> {
    const platform = this.state.platforms.find((p) => p.id === platformId);

    if (!platform || !platform.connected) {
      return false;
    }

    const connectedLinks = platform.links.filter((link) => link.status === 'connected');

    for (const link of connectedLinks) {
      this.expireLink(link.linkId);
    }

    const updatedPlatforms = this.state.platforms.map((p) =>
      p.id === platformId
        ? {
            ...p,
            connected: false,
            sessionData: undefined,
            retryCount: 0,
          }
        : p,
    );

    this.setState({
      platforms: updatedPlatforms,
    });

    console.log(`[CoordinatorDO] Disconnected platform: ${platformId}`);
    return true;
  }

  /**
   * Delete ALL connections for this user across all platforms.
   * Intended for user account deletion cleanup.
   */
  async deleteAllConnections(): Promise<{ deletedAgents: string[]; platformsCleared: number }> {
    const deletedAgents: string[] = [];

    // Collect all agent IDs from all links
    for (const platform of this.state.platforms) {
      for (const link of platform.links) {
        if (link.agentId) {
          try {
            await this.deleteAgentDO(link.agentId);
            deletedAgents.push(link.agentId);
          } catch (e) {
            console.error(`[CoordinatorDO] Failed deleting agent ${link.agentId}:`, e);
          }
        }
      }
    }

    const platformsCleared = this.state.platforms.length;
    // Clear all platforms/state
    this.setState({ platforms: [] });

    console.log(
      `[CoordinatorDO] Deleted all connections. Agents deleted: ${deletedAgents.length}, platforms cleared: ${platformsCleared}`,
    );
    return { deletedAgents, platformsCleared };
  }

  /**
   * Get Agent DO identifier for a link
   */
  getAgentIdForLink(linkId: string): string | null {
    // Find the link across all platforms
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.linkId === linkId);
      if (link) {
        return link.agentId || null;
      }
    }
    return null;
  }

  async getPlatformConnections(): Promise<ConnectionsResponse> {
    let connections: Connection[] = [];

    Object.entries(platformDetails).forEach((platform) => {
      const platformId = platform[0] as PlatformTypes;
      const platformData = platform[1];

      connections.push({
        id: platformId,
        name: platformData.name,
        connected: this.state.platforms.find((p) => p.id === platformId)?.connected || false,
        logo: platformData.logo,
      });
    });

    return { connections };
  }

  async getPlatformConnection(platformId: PlatformTypes): Promise<Connection> {
    const platformData = platformDetails[platformId];
    if (!platformData) {
      throw new Error(`Platform ${platformId} not found in platformDetails`);
    }
    return {
      id: platformId,
      name: platformData.name,
      connected: this.state.platforms.find((p) => p.id === platformId)?.connected || false,
      logo: platformData.logo,
    };
  }

  // RPC Methods for session data management

  /**
   * Get session data for a platform and user
   */
  async getSessionData(platformId: PlatformTypes): Promise<UserSessionData | null> {
    const platform = this.state.platforms.find((p) => p.id === platformId);
    if (!platform || !platform.sessionData) {
      return null;
    }
    return platform.sessionData || null;
  }

  /**
   * Update session data for a platform and user
   */
  async updateSessionData(platformId: PlatformTypes, sessionData: UserSessionData): Promise<void> {
    const platformIndex = this.state.platforms.findIndex((p) => p.id === platformId);

    if (platformIndex === -1) {
      console.error('[CoordinatorDO] Platform not found for session data update:', platformId);
    } else {
      // Update existing platform
      const updatedPlatforms = [...this.state.platforms];
      const platform = updatedPlatforms[platformIndex];

      updatedPlatforms[platformIndex] = {
        ...platform,
        sessionData: sessionData,
      };

      this.setState({
        platforms: updatedPlatforms,
      });
    }
  }

  /**
   * Clear session data for a platform and user
   */
  async clearSessionData(platformId: PlatformTypes): Promise<void> {
    const platformIndex = this.state.platforms.findIndex((p) => p.id === platformId);

    if (platformIndex === -1) {
      return;
    }

    const updatedPlatforms = [...this.state.platforms];
    const platform = updatedPlatforms[platformIndex];

    if (platform.sessionData) {
      updatedPlatforms[platformIndex] = {
        ...platform,
        sessionData: undefined,
      };

      this.setState({
        platforms: updatedPlatforms,
      });
    }
  }
}
