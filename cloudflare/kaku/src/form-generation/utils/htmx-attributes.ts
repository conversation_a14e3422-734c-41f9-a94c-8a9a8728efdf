/**
 * HTMX attribute generation utilities
 */

import type { FormButton } from '../../agent/types';

/**
 * Generate HTMX attributes for form buttons
 */
export function getButtonHTMXAttributes(button: FormButton): string {
  const attrs: string[] = [];

  attrs.push('hx-ws="send"');
  attrs.push('hx-target="#connection-flow"');
  attrs.push('hx-swap="innerHTML"');

  if (button.type !== 'submit' && !button.isSocialLogin) {
    attrs.push('hx-trigger="click"');
    attrs.push(
      `hx-vals='{"type": "form_submission", "clickId": "${button.id}", "interaction": "click"}'`,
    );
  } else {
    attrs.push(
      `hx-vals='{"type": "form_submission", "clickId": "${button.id}", "interaction": "submit"}'`,
    );
  }

  return attrs.join(' ');
}
